# Hello Edge

A minimal [Nuxt](https://nuxt.com) starter deployed on the Edge using [NuxtHub](https://hub.nuxt.com).

https://hello.nuxt.dev

<a href="https://hello.nuxt.dev">
<img src="https://github.com/nuxt-hub/hello-edge/assets/904724/99d1bd54-ef7e-4ac9-83ad-0a290f85edcf" alt="Hello World template for NuxtHub" />
</a>

## Features

- Server-Side rendering on Cloudflare Workers
- Biome for code formatting and linting
- Lefthook for Git hooks automation
- Ready to add a database, blob and KV storage
- One click deploy on 275+ locations for free

## Setup

Make sure to install the dependencies with [Bun](https://bun.sh/):

```bash
bun install
```

You can update the main text displayed by creating a `.env`:

```bash
NUXT_PUBLIC_HELLO_TEXT="Hello my world!"
```

### Code Quality Tools

This project uses [Biome](https://biomejs.dev/) for code formatting and linting, and [Lefthook](https://github.com/evilmartians/lefthook) for Git hooks automation.

#### Biome

Biome is a fast formatter and linter for JavaScript, TypeScript, JSX, and JSON. You can run Biome manually with these commands:

```bash
# Format code
bun run format

# Lint code
bun run biome:lint

# Format and lint code
bun run biome:check
```

#### Lefthook

Lefthook automatically runs Biome before each commit to ensure code quality. The Git hooks are installed automatically when you run `bun install`. If you need to install them manually, run:

```bash
bunx lefthook install
```

With Lefthook configured:
- Pre-commit: Automatically formats and lints staged files
- Pre-push: Runs Biome CI checks on the entire codebase

## Development Server

Start the development server on `http://localhost:3000`:

```bash
pnpm dev
```

## Production

Build the application for production:

```bash
pnpm build
```

## Deploy


Deploy the application on the Edge with [NuxtHub](https://hub.nuxt.com) on your Cloudflare account:

```bash
npx nuxthub deploy
```

Then checkout your server logs, analaytics and more in the [NuxtHub Admin](https://admin.hub.nuxt.com).

You can also deploy using [Cloudflare Pages CI](https://hub.nuxt.com/docs/getting-started/deploy#cloudflare-pages-ci).

