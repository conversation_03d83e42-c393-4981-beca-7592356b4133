# DeFi Portfolio AI Agent - Neo-Brutalism UI Development Prompts

## Design System Overview

**Neo-Brutalism Retro Aesthetic Guidelines:**
- Heavy black borders (4-8px thick)
- Sharp, angular corners with no border-radius
- High contrast color combinations
- Bold, chunky typography (Inter Black, JetBrains Mono)
- Vibrant neon accents: Electric lime (#00FF41), Hot pink (#FF0080), <PERSON><PERSON> (#00FFFF), Orange (#FF6B00)
- Dark base: Deep black (#000000) and charcoal (#1A1A1A)
- Harsh drop shadows (8px offset, no blur)
- Pixelated elements and retro computer aesthetics
- Deliberately "ugly" beautiful design philosophy

---

## 1. Landing Page Prompt

Create a neo-brutalist landing page for a DeFi Portfolio AI Agent with the following specifications:

**Visual Style:**
- Full-screen hero section with aggressive black borders and electric lime (#00FF41) accent color
- Header with chunky navigation: "POR<PERSON><PERSON><PERSON>", "FEATURES", "PRICING", "DOCS" in bold black text on white background
- Main headline in massive, brutal typography: "AI-POWERED DEFI DOMINATION" with harsh drop shadow
- Subheading in retro terminal font explaining automated portfolio management
- CTA button: Oversized black rectangle with hot pink (#FF0080) background and white text "CONNECT WALLET"

**Layout Elements:**
- Three-column feature grid with thick black borders separating each section
- Each feature card: White background, black borders, vibrant accent colors (cyan, orange, lime)
- Terminal-style code snippets showing portfolio returns
- Pixelated graphs showing performance metrics
- Footer with brutal grid layout displaying supported chains (Polygon, Base) as badge-style elements

**Interactive Elements:**
- Hover effects that add additional border thickness
- Color inversions on button hover (background/text swap)
- Subtle glitch effects on the main headline
- Wallet connection modal with retro terminal styling

---

## 2. Dashboard/Portfolio Overview Prompt

Design a brutal dashboard interface for portfolio management:

**Layout Structure:**
- Fixed sidebar navigation with chunky buttons (black borders, white background)
- Main content area split into harsh rectangular sections
- No rounded corners anywhere - everything sharp and angular

**Key Components:**
- Portfolio value display: Massive numbers in brutal typography with neon cyan (#00FFFF) color
- Asset allocation pie chart: Pixelated/low-res aesthetic with vibrant sector colors
- Performance graph: Sharp, angular line chart with electric lime data points
- Chain distribution: Horizontal bars with thick black borders and chain-specific colors
- Recent transactions table: Monospace font, alternating row colors (black/white stripe pattern)

**Visual Hierarchy:**
- Section headers: Bold, all-caps text with underlines made of repeated dashes
- Data cards: White background, thick black borders, colored accent strips on left edge
- Action buttons: Rectangular blocks with harsh drop shadows
- Status indicators: Square badges with high-contrast colors (success: lime, warning: orange, error: hot pink)

**Color Distribution:**
- Primary: Black text on white background
- Accents: Electric lime for gains, hot pink for losses, cyan for neutral data
- Borders: Consistent 4px black borders throughout

---

## 3. Strategy Selection/Configuration Prompt

Create a strategy configuration page with retro-futuristic brutalism:

**Visual Theme:**
- Matrix-style terminal interface mixed with brutal web design
- Three strategy cards arranged horizontally: "CONSERVATIVE", "BALANCED", "AGGRESSIVE"
- Each card: Different neon accent color (lime, cyan, orange), thick borders, harsh shadows

**Interactive Elements:**
- Risk tolerance slider: Chunky rectangular handle on thick black track
- Portfolio allocation bars: Rectangular segments with clear separations
- Protocol selection: Checkbox grid with oversized square checkboxes
- Configuration toggles: Brutal on/off switches (rectangular, high contrast)

**Typography & Data:**
- Strategy descriptions in monospace font
- APY projections in large, bold numbers with neon glow effects
- Risk metrics displayed as pixelated bar graphs
- Historical performance: Retro-style terminal readouts

**Form Elements:**
- Input fields: Black borders, white backgrounds, monospace font
- Dropdown menus: Sharp rectangles with black arrows
- Submit button: Oversized with "DEPLOY STRATEGY" text and electric lime background

---

## 4. Transaction History Prompt

Design a transaction history page with terminal/console aesthetics:

**Interface Style:**
- Full-width table with alternating black/white row stripes
- Column headers: Bold, uppercase text with thick underlines
- Fixed-width monospace font for all data
- Harsh vertical dividers between columns (thick black lines)

**Data Visualization:**
- Transaction type icons: Pixelated symbols (arrows, plus/minus signs)
- Chain badges: Sharp rectangular tags with network colors
- Status indicators: Square colored blocks (success/pending/failed)
- Amount displays: Large, bold numbers with currency symbols

**Filtering & Search:**
- Filter buttons: Rectangular tags with toggle states (selected = inverted colors)
- Search bar: Thick black border, terminal-style cursor
- Date range picker: Brutal calendar widget with sharp edges

**Additional Elements:**
- Export button: "DOWNLOAD CSV" in chunky typography
- Pagination: Square number buttons with sharp edges
- Load more: "FETCH MORE DATA" button in hot pink
- Empty state: ASCII art with retro computer graphics

---

## 5. Analytics/Performance Page Prompt

Create a data-heavy analytics dashboard with retro computing vibes:

**Chart Styles:**
- All charts: Sharp, angular lines with no smoothing
- Candlestick charts: Pixelated bars in neon colors
- Area charts: Solid fills with harsh color transitions
- Performance metrics: Large numerical displays with terminal font

**Dashboard Layout:**
- Grid system: 4x4 layout with thick black dividers
- Each widget: White background, black borders, colored accent headers
- Time period selector: Chunky button group (1D, 7D, 30D, 1Y)
- Comparison tools: Side-by-side brutal data cards

**Data Presentation:**
- APY trends: Sharp line graphs with electric lime trend lines
- Risk metrics: Horizontal bar charts with high contrast colors
- Protocol performance: Leaderboard-style ranking with position numbers in circles
- Gas optimization savings: Before/after comparison in split-screen layout

**Visual Hierarchy:**
- Primary metrics: Huge numbers with neon glow effects
- Secondary data: Medium-sized monospace text
- Tertiary info: Small caps labels with dashed underlines
- Alert notifications: Sharp-edged banners with warning colors

---

## 6. Settings/Profile Page Prompt

Design a user settings interface with cyberpunk brutalism:

**Profile Section:**
- User avatar: Pixelated square image with thick black border
- Account details: Terminal-style readout with label/value pairs
- Connection status: Large status badge with chain network indicators

**Settings Categories:**
- Tabbed interface: Sharp rectangular tabs with harsh active states
- Risk preferences: Slider controls with chunky handles
- Notification settings: Oversized toggle switches in grid layout
- API access: Code-style display with monospace font and copy buttons

**Security Features:**
- Two-factor authentication: QR code display with pixelated styling
- Wallet management: Connected wallets as card-style blocks
- Session management: Active sessions table with terminal styling
- Backup options: Download buttons for recovery phrases

**Form Design:**
- Input labels: Bold, uppercase text
- Text inputs: Thick borders, no border-radius, monospace font
- Select dropdowns: Sharp arrows, high contrast options
- Save button: "UPDATE PROFILE" in electric lime with harsh shadow

---

## 7. Onboarding/Wallet Connection Prompt

Create a step-by-step onboarding flow with retro-futuristic styling:

**Welcome Screen:**
- Large ASCII art logo or retro computer graphics
- Progress indicator: Sharp rectangular steps with completed states in neon colors
- Welcome message: Terminal-style typing effect with monospace font

**Wallet Connection:**
- Supported wallets: Grid of sharp-edged cards with wallet logos
- Each wallet card: White background, thick black border, hover effects
- Connection status: Large success/error messages with appropriate colors
- QR code display: Pixelated styling for mobile wallet connections

**Risk Assessment:**
- Question cards: Brutal quiz interface with sharp edges
- Multiple choice: Oversized radio buttons with square styling
- Progress bar: Chunky rectangular segments that fill with color
- Results display: Terminal-style output with investment profile

**Portfolio Setup:**
- Asset selection: Checkbox grid with crypto logos and sharp borders
- Initial allocation: Horizontal sliders with chunky handles
- Preview display: Sharp-edged summary cards
- Final confirmation: Large "INITIALIZE PORTFOLIO" button

---

## 8. Mobile-Responsive Design Prompt

Adapt the neo-brutalist design for mobile interfaces:

**Mobile Navigation:**
- Hamburger menu: Thick lines forming sharp corners
- Slide-out menu: Full-screen overlay with chunky navigation buttons
- Bottom tab bar: Square icons with sharp active states

**Mobile Dashboard:**
- Stacked card layout: Full-width cards with thick borders
- Swipeable sections: Sharp page indicators
- Touch-friendly buttons: Larger tap targets with brutal styling
- Performance charts: Simplified but maintaining sharp, angular aesthetic

**Mobile Forms:**
- Larger input fields: Thick borders, high contrast
- Mobile-optimized selectors: Native dropdown styling adapted to brutal theme
- Touch sliders: Chunky handles easy to manipulate on small screens

**Responsive Behavior:**
- Breakpoints: Sharp transitions between layouts (no gradual scaling)
- Typography scaling: Maintains brutalist hierarchy at all sizes
- Color consistency: Same vibrant neon accents across all screen sizes
- Touch interactions: Harsh visual feedback for all interactive elements

---

## Development Notes:

**CSS Framework Recommendations:**
- Use Tailwind CSS with custom brutal utilities
- Create sharp shadow utilities (no blur values)
- Define harsh border utilities (4px, 6px, 8px standard)
- Custom color palette with neon values

**Typography Stack:**
- Primary: Inter Black/Extra Bold
- Monospace: JetBrains Mono or Courier New
- Display: Custom pixelated fonts for headers

**Animation Guidelines:**
- No easing functions (linear only)
- Sharp state transitions
- Glitch effects for emphasis
- Terminal-style typing animations

**Component Library:**
- All components sharp-edged (border-radius: 0)
- High contrast color combinations
- Consistent border weights
- Harsh drop shadows throughout